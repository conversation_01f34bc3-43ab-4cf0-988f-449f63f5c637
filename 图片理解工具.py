#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import requests
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import io
import os

class ImageAnalyzer:
    def __init__(self):
        self.api_url = "https://free.v36.cm"
        self.api_key = "sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65"
        self.prompt = """请详细描述所提供图片的内容。我希望描述的结构如下：

1. 逐层分解图片。例如："背景是灰色的路面。前景是一只手拿着纸盘。"
2. 列出图片中的每个元素，并附上大致的位置参考。
3. 根据之前生成的描述列表，以优雅的散文风格描述图片内容。
4. **您提供的提示必须以**英语**返回，仅输出最终的图片描述文本，不包含任何列表文本。**"""
        
        self.selected_image_path = None
        self.setup_gui()
    
    def setup_gui(self):
        """设置图形界面"""
        self.root = tk.Tk()
        self.root.title("ChatGPT 图片理解工具")
        self.root.geometry("800x700")
        self.root.configure(bg="#f0f0f0")
        
        # 标题
        title_label = tk.Label(
            self.root, 
            text="🖼️ ChatGPT 图片理解工具", 
            font=("微软雅黑", 18, "bold"),
            bg="#f0f0f0",
            fg="#333333"
        )
        title_label.pack(pady=20)
        
        # 副标题
        subtitle_label = tk.Label(
            self.root,
            text="使用 GPT-4o-mini 模型深度分析您的图片内容",
            font=("微软雅黑", 10),
            bg="#f0f0f0",
            fg="#666666"
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 选择图片按钮
        self.select_btn = tk.Button(
            self.root,
            text="📁 选择图片文件",
            font=("微软雅黑", 12),
            bg="#4CAF50",
            fg="white",
            relief="flat",
            padx=20,
            pady=10,
            command=self.select_image
        )
        self.select_btn.pack(pady=10)
        
        # 图片预览区域
        self.preview_frame = tk.Frame(self.root, bg="#f0f0f0")
        self.preview_frame.pack(pady=10, fill="both", expand=False)
        
        self.preview_label = tk.Label(
            self.preview_frame,
            text="未选择图片",
            font=("微软雅黑", 10),
            bg="#f0f0f0",
            fg="#999999"
        )
        self.preview_label.pack()
        
        # 分析按钮
        self.analyze_btn = tk.Button(
            self.root,
            text="🔍 开始分析图片",
            font=("微软雅黑", 12),
            bg="#2196F3",
            fg="white",
            relief="flat",
            padx=20,
            pady=10,
            state="disabled",
            command=self.analyze_image
        )
        self.analyze_btn.pack(pady=10)
        
        # 状态标签
        self.status_label = tk.Label(
            self.root,
            text="请选择一张图片开始分析",
            font=("微软雅黑", 10),
            bg="#f0f0f0",
            fg="#666666"
        )
        self.status_label.pack(pady=5)
        
        # 结果显示区域
        result_frame = tk.Frame(self.root, bg="#f0f0f0")
        result_frame.pack(pady=10, fill="both", expand=True, padx=20)
        
        result_title = tk.Label(
            result_frame,
            text="📝 分析结果：",
            font=("微软雅黑", 12, "bold"),
            bg="#f0f0f0",
            fg="#333333"
        )
        result_title.pack(anchor="w", pady=(0, 5))
        
        self.result_text = scrolledtext.ScrolledText(
            result_frame,
            font=("微软雅黑", 10),
            wrap=tk.WORD,
            height=15,
            bg="white",
            fg="#333333",
            relief="solid",
            borderwidth=1
        )
        self.result_text.pack(fill="both", expand=True)
        
        # 重置按钮
        self.reset_btn = tk.Button(
            self.root,
            text="🔄 重新选择",
            font=("微软雅黑", 10),
            bg="#FF9800",
            fg="white",
            relief="flat",
            padx=15,
            pady=5,
            command=self.reset_tool
        )
        self.reset_btn.pack(pady=10)
    
    def select_image(self):
        """选择图片文件"""
        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            self.selected_image_path = file_path
            self.display_preview(file_path)
            self.analyze_btn.config(state="normal")
            self.status_label.config(text=f"已选择: {os.path.basename(file_path)}")
    
    def display_preview(self, image_path):
        """显示图片预览"""
        try:
            # 打开并调整图片大小
            image = Image.open(image_path)
            
            # 计算缩放比例，保持宽高比
            max_size = (300, 200)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 转换为Tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 更新预览标签
            self.preview_label.config(image=photo, text="")
            self.preview_label.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"无法预览图片: {str(e)}")
    
    def image_to_base64(self, image_path):
        """将图片转换为base64编码"""
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return f"data:image/jpeg;base64,{encoded_string}"
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    def analyze_image(self):
        """分析图片"""
        if not self.selected_image_path:
            messagebox.showwarning("警告", "请先选择一张图片！")
            return
        
        # 更新状态
        self.status_label.config(text="正在分析图片，请稍候...")
        self.analyze_btn.config(state="disabled", text="分析中...")
        self.root.update()
        
        try:
            # 转换图片为base64
            base64_image = self.image_to_base64(self.selected_image_path)
            
            # 准备API请求
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": base64_image
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.7
            }
            
            # 发送请求
            response = requests.post(
                f"{self.api_url}/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("choices") and len(result["choices"]) > 0:
                    analysis_result = result["choices"][0]["message"]["content"]
                    self.display_result(analysis_result)
                    self.status_label.config(text="分析完成！")
                else:
                    raise Exception("API返回数据格式错误")
            else:
                raise Exception(f"API请求失败: {response.status_code} {response.text}")
                
        except Exception as e:
            error_msg = f"分析失败: {str(e)}"
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, error_msg)
            self.status_label.config(text="分析失败")
            messagebox.showerror("错误", error_msg)
        
        finally:
            self.analyze_btn.config(state="normal", text="🔍 开始分析图片")
    
    def display_result(self, result):
        """显示分析结果"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, result)
    
    def reset_tool(self):
        """重置工具"""
        self.selected_image_path = None
        self.preview_label.config(image="", text="未选择图片")
        self.preview_label.image = None
        self.analyze_btn.config(state="disabled")
        self.status_label.config(text="请选择一张图片开始分析")
        self.result_text.delete(1.0, tk.END)
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = ImageAnalyzer()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
