<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT 图片理解工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-section:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-section.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }

        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .preview-section {
            display: none;
            margin-bottom: 30px;
        }

        .preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .analyze-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            display: none;
            margin-top: 30px;
        }

        .result-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .result-content {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            line-height: 1.8;
            font-size: 1.1em;
            color: #444;
            border-left: 4px solid #667eea;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .reset-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            margin-top: 15px;
            transition: background 0.3s ease;
        }

        .reset-btn:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .upload-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ ChatGPT 图片理解工具</h1>
            <p>使用 GPT-4o-mini 模型深度分析您的图片内容</p>
        </div>
        
        <div class="content">
            <div class="upload-section" id="uploadSection">
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击选择图片或拖拽图片到此处</div>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择图片文件
                </button>
                <input type="file" id="fileInput" class="file-input" accept="image/*">
            </div>
            
            <div class="preview-section" id="previewSection">
                <img id="previewImage" class="preview-image" alt="预览图片">
                <button class="analyze-btn" id="analyzeBtn">
                    🔍 开始分析图片
                </button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在分析图片，请稍候...</p>
            </div>
            
            <div class="result-section" id="resultSection">
                <h3 class="result-title">📝 图片分析结果</h3>
                <div class="result-content" id="resultContent"></div>
                <button class="reset-btn" onclick="resetTool()">重新分析</button>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'https://free.v36.cm';
        const API_KEY = 'sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65';
        
        const imageUnderstandPrompt = `请详细描述所提供图片的内容。我希望描述的结构如下：

1. 逐层分解图片。例如："背景是灰色的路面。前景是一只手拿着纸盘。"
2. 列出图片中的每个元素，并附上大致的位置参考。
3. 根据之前生成的描述列表，以优雅的散文风格描述图片内容。
4. **您提供的提示必须以**英语**返回，仅输出最终的图片描述文本，不包含任何列表文本。**`;

        let selectedFile = null;
        let base64Image = null;

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        
        // 拖拽上传功能
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择有效的图片文件！');
                return;
            }

            if (file.size > 10 * 1024 * 1024) {
                alert('图片文件大小不能超过10MB！');
                return;
            }

            selectedFile = file;
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                base64Image = e.target.result;
                document.getElementById('previewImage').src = base64Image;
                document.getElementById('previewSection').style.display = 'block';
                document.getElementById('uploadSection').style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        // 分析按钮点击事件
        document.getElementById('analyzeBtn').addEventListener('click', analyzeImage);

        async function analyzeImage() {
            if (!base64Image) {
                alert('请先选择一张图片！');
                return;
            }

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('analyzeBtn').disabled = true;
            document.getElementById('resultSection').style.display = 'none';

            try {
                const response = await fetch(`${API_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        type: 'text',
                                        text: imageUnderstandPrompt
                                    },
                                    {
                                        type: 'image_url',
                                        image_url: {
                                            url: base64Image
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 1000,
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.choices && data.choices.length > 0) {
                    const result = data.choices[0].message.content;
                    displayResult(result);
                } else {
                    throw new Error('API返回数据格式错误');
                }

            } catch (error) {
                console.error('分析图片时出错:', error);
                displayError(`分析失败: ${error.message}`);
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        function displayResult(result) {
            const resultContent = document.getElementById('resultContent');
            resultContent.textContent = result;
            resultContent.classList.remove('error');
            document.getElementById('resultSection').style.display = 'block';
        }

        function displayError(errorMessage) {
            const resultContent = document.getElementById('resultContent');
            resultContent.textContent = errorMessage;
            resultContent.classList.add('error');
            document.getElementById('resultSection').style.display = 'block';
        }

        function resetTool() {
            selectedFile = null;
            base64Image = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('uploadSection').style.display = 'block';
            document.getElementById('analyzeBtn').disabled = false;
        }
    </script>
</body>
</html>
